package balance

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"

	"keyhub/internal/database"
	"keyhub/internal/models"
)

// BalanceService 余额查询服务
type BalanceService struct{}

// NewBalanceService 创建余额查询服务
func NewBalanceService() *BalanceService {
	return &BalanceService{}
}

// BalanceInfo 余额信息
type BalanceInfo struct {
	TotalBalance  *float64 `json:"total_balance"`
	ChargeBalance *float64 `json:"charge_balance"`
	GiftBalance   *float64 `json:"gift_balance"`
	Currency      string   `json:"currency"`
}

// QueryKeyBalance 查询密钥余额
func (s *BalanceService) QueryKeyBalance(keyID uint) (*models.TestLog, error) {
	db := database.GetDB()
	
	// 获取密钥和提供商信息
	var key models.Key
	if err := db.Preload("Provider").First(&key, keyID).Error; err != nil {
		return nil, fmt.Errorf("密钥不存在: %w", err)
	}
	
	// 检查提供商是否支持余额查询
	if !key.Provider.SupportsBalance {
		return nil, fmt.Errorf("提供商不支持余额查询")
	}
	
	// 检查提供商是否配置了余额查询端点
	if key.Provider.BalanceEndpoint == "" {
		return nil, fmt.Errorf("提供商未配置余额查询端点")
	}
	
	// 开始查询
	startTime := time.Now()
	testLog := &models.TestLog{
		KeyID:    keyID,
		TestType: models.TestTypeBalance,
	}
	
	// 查询余额
	balanceInfo, err := s.queryBalance(key)
	duration := time.Since(startTime).Milliseconds()
	testLog.Duration = duration
	
	if err != nil {
		testLog.Status = models.TestStatusFailed
		testLog.ErrorMessage = err.Error()
	} else {
		testLog.Status = models.TestStatusSuccess
		testLog.ResponseData = models.ResponseData{
			"total_balance":  balanceInfo.TotalBalance,
			"charge_balance": balanceInfo.ChargeBalance,
			"gift_balance":   balanceInfo.GiftBalance,
			"currency":       balanceInfo.Currency,
		}
		
		// 更新密钥余额信息
		now := time.Now()
		key.TotalBalance = balanceInfo.TotalBalance
		key.ChargeBalance = balanceInfo.ChargeBalance
		key.GiftBalance = balanceInfo.GiftBalance
		key.Currency = balanceInfo.Currency
		key.BalanceUpdatedAt = &now
		
		if err := db.Save(&key).Error; err != nil {
			return nil, fmt.Errorf("更新密钥余额失败: %w", err)
		}
	}
	
	// 保存测试日志
	if err := db.Create(testLog).Error; err != nil {
		return nil, fmt.Errorf("保存测试日志失败: %w", err)
	}
	
	return testLog, nil
}

// queryBalance 查询余额的具体实现
func (s *BalanceService) queryBalance(key models.Key) (*BalanceInfo, error) {
	switch key.Provider.Name {
	case "OpenAI":
		return s.queryOpenAIBalance(key)
	case "硅基流动":
		return s.querySiliconFlowBalance(key)
	default:
		return s.queryGenericBalance(key)
	}
}

// queryOpenAIBalance 查询OpenAI余额
func (s *BalanceService) queryOpenAIBalance(key models.Key) (*BalanceInfo, error) {
	url := key.Provider.BaseURL + key.Provider.BalanceEndpoint
	
	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %v", err)
	}
	
	req.Header.Set("Authorization", fmt.Sprintf("%s %s", key.Provider.AuthType, key.KeyValue))
	
	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("请求失败: %v", err)
	}
	defer resp.Body.Close()
	
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %v", err)
	}
	
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("HTTP %d: %s", resp.StatusCode, string(body))
	}
	
	var response struct {
		TotalGranted float64 `json:"total_granted"`
		TotalUsed    float64 `json:"total_used"`
		TotalAvailable float64 `json:"total_available"`
	}
	
	if err := json.Unmarshal(body, &response); err != nil {
		return nil, fmt.Errorf("解析响应失败: %v", err)
	}
	
	totalBalance := response.TotalAvailable
	return &BalanceInfo{
		TotalBalance: &totalBalance,
		Currency:     "USD",
	}, nil
}

// querySiliconFlowBalance 查询硅基流动余额
func (s *BalanceService) querySiliconFlowBalance(key models.Key) (*BalanceInfo, error) {
	url := key.Provider.BaseURL + key.Provider.BalanceEndpoint
	
	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %v", err)
	}
	
	req.Header.Set("Authorization", fmt.Sprintf("%s %s", key.Provider.AuthType, key.KeyValue))
	
	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("请求失败: %v", err)
	}
	defer resp.Body.Close()
	
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %v", err)
	}
	
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("HTTP %d: %s", resp.StatusCode, string(body))
	}
	
	var response struct {
		Data struct {
			ChargeBalance float64 `json:"chargeBalance"`
			GiftBalance   float64 `json:"giftBalance"`
		} `json:"data"`
	}
	
	if err := json.Unmarshal(body, &response); err != nil {
		return nil, fmt.Errorf("解析响应失败: %v", err)
	}
	
	chargeBalance := response.Data.ChargeBalance
	giftBalance := response.Data.GiftBalance
	totalBalance := chargeBalance + giftBalance
	
	return &BalanceInfo{
		TotalBalance:  &totalBalance,
		ChargeBalance: &chargeBalance,
		GiftBalance:   &giftBalance,
		Currency:      "CNY",
	}, nil
}

// queryGenericBalance 通用余额查询
func (s *BalanceService) queryGenericBalance(key models.Key) (*BalanceInfo, error) {
	url := key.Provider.BaseURL + key.Provider.BalanceEndpoint
	
	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %v", err)
	}
	
	req.Header.Set("Authorization", fmt.Sprintf("%s %s", key.Provider.AuthType, key.KeyValue))
	
	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("请求失败: %v", err)
	}
	defer resp.Body.Close()
	
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %v", err)
	}
	
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("HTTP %d: %s", resp.StatusCode, string(body))
	}
	
	var response map[string]interface{}
	if err := json.Unmarshal(body, &response); err != nil {
		return nil, fmt.Errorf("解析响应失败: %v", err)
	}
	
	// 尝试从响应中提取余额信息
	// 这里可以根据不同提供商的响应格式进行适配
	balanceInfo := &BalanceInfo{
		Currency: key.Provider.Currency,
	}
	
	// 尝试常见的余额字段
	if balance, ok := response["balance"].(float64); ok {
		balanceInfo.TotalBalance = &balance
	} else if total, ok := response["total_balance"].(float64); ok {
		balanceInfo.TotalBalance = &total
	}
	
	return balanceInfo, nil
}

// BatchQueryBalance 批量查询余额
func (s *BalanceService) BatchQueryBalance(keyIDs []uint) ([]*models.TestLog, error) {
	var results []*models.TestLog
	
	for _, keyID := range keyIDs {
		testLog, err := s.QueryKeyBalance(keyID)
		if err != nil {
			// 创建失败的测试日志
			failedLog := &models.TestLog{
				KeyID:        keyID,
				TestType:     models.TestTypeBalance,
				Status:       models.TestStatusFailed,
				ErrorMessage: err.Error(),
				Duration:     0,
			}
			results = append(results, failedLog)
		} else {
			results = append(results, testLog)
		}
		
		// 添加延迟避免请求过于频繁
		time.Sleep(200 * time.Millisecond)
	}
	
	return results, nil
}
