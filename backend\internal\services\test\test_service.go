package test

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"

	"keyhub/internal/database"
	"keyhub/internal/models"
)

// TestService 测试服务
type TestService struct{}

// NewTestService 创建测试服务
func NewTestService() *TestService {
	return &TestService{}
}

// TestKeyConnectivity 测试密钥连通性
func (s *TestService) TestKeyConnectivity(keyID uint) (*models.TestLog, error) {
	db := database.GetDB()
	
	// 获取密钥和提供商信息
	var key models.Key
	if err := db.Preload("Provider").First(&key, keyID).Error; err != nil {
		return nil, fmt.Errorf("密钥不存在: %w", err)
	}
	
	// 检查提供商是否配置了测试端点
	if key.Provider.TestEndpoint == "" {
		return nil, fmt.Errorf("提供商未配置测试端点")
	}
	
	// 开始测试
	startTime := time.Now()
	testLog := &models.TestLog{
		KeyID:    keyID,
		TestType: models.TestTypeConnectivity,
	}
	
	// 构建测试请求
	testURL := key.Provider.BaseURL + key.Provider.TestEndpoint
	requestBody := s.buildTestRequest(key.Provider)
	
	// 发送请求
	success, responseData, errorMsg := s.sendTestRequest(testURL, key.KeyValue, key.Provider.AuthType, requestBody)
	
	// 计算耗时
	duration := time.Since(startTime).Milliseconds()
	
	// 更新测试日志
	testLog.Duration = duration
	testLog.ResponseData = responseData
	if success {
		testLog.Status = models.TestStatusSuccess
	} else {
		testLog.Status = models.TestStatusFailed
		testLog.ErrorMessage = errorMsg
	}
	
	// 保存测试日志
	if err := db.Create(testLog).Error; err != nil {
		return nil, fmt.Errorf("保存测试日志失败: %w", err)
	}
	
	// 更新密钥状态
	now := time.Now()
	key.LastTestTime = &now
	if success {
		key.Status = models.KeyStatusValid
	} else {
		key.Status = models.KeyStatusInvalid
	}
	
	if err := db.Save(&key).Error; err != nil {
		return nil, fmt.Errorf("更新密钥状态失败: %w", err)
	}
	
	return testLog, nil
}

// buildTestRequest 构建测试请求体
func (s *TestService) buildTestRequest(provider models.Provider) map[string]interface{} {
	// 默认测试请求
	request := map[string]interface{}{
		"model":      "gpt-3.5-turbo",
		"messages": []map[string]interface{}{
			{
				"role":    "user",
				"content": "hi",
			},
		},
		"max_tokens":  1,
		"temperature": 0.1,
	}
	
	// 使用提供商配置覆盖默认值
	if provider.Config != nil {
		for key, value := range provider.Config {
			request[key] = value
		}
	}
	
	return request
}

// sendTestRequest 发送测试请求
func (s *TestService) sendTestRequest(url, apiKey, authType string, requestBody map[string]interface{}) (bool, models.ResponseData, string) {
	// 序列化请求体
	jsonData, err := json.Marshal(requestBody)
	if err != nil {
		return false, nil, fmt.Sprintf("序列化请求失败: %v", err)
	}
	
	// 创建HTTP请求
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		return false, nil, fmt.Sprintf("创建请求失败: %v", err)
	}
	
	// 设置请求头
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", fmt.Sprintf("%s %s", authType, apiKey))
	
	// 发送请求
	client := &http.Client{
		Timeout: 30 * time.Second,
	}
	
	resp, err := client.Do(req)
	if err != nil {
		return false, nil, fmt.Sprintf("请求失败: %v", err)
	}
	defer resp.Body.Close()
	
	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return false, nil, fmt.Sprintf("读取响应失败: %v", err)
	}
	
	// 解析响应
	var responseData models.ResponseData
	if err := json.Unmarshal(body, &responseData); err != nil {
		return false, nil, fmt.Sprintf("解析响应失败: %v", err)
	}
	
	// 判断是否成功
	success := resp.StatusCode == http.StatusOK
	if !success {
		errorMsg := fmt.Sprintf("HTTP %d", resp.StatusCode)
		if errMsg, ok := responseData["error"]; ok {
			if errMap, ok := errMsg.(map[string]interface{}); ok {
				if message, ok := errMap["message"].(string); ok {
					errorMsg = message
				}
			}
		}
		return false, responseData, errorMsg
	}
	
	// 检查响应是否包含预期字段
	if _, hasChoices := responseData["choices"]; !hasChoices {
		return false, responseData, "响应格式不正确"
	}
	
	return true, responseData, ""
}

// BatchTestKeys 批量测试密钥
func (s *TestService) BatchTestKeys(keyIDs []uint) ([]*models.TestLog, error) {
	var results []*models.TestLog
	
	for _, keyID := range keyIDs {
		testLog, err := s.TestKeyConnectivity(keyID)
		if err != nil {
			// 创建失败的测试日志
			failedLog := &models.TestLog{
				KeyID:        keyID,
				TestType:     models.TestTypeConnectivity,
				Status:       models.TestStatusFailed,
				ErrorMessage: err.Error(),
				Duration:     0,
			}
			results = append(results, failedLog)
		} else {
			results = append(results, testLog)
		}
		
		// 添加延迟避免请求过于频繁
		time.Sleep(100 * time.Millisecond)
	}
	
	return results, nil
}

// GetTestLogs 获取测试日志
func (s *TestService) GetTestLogs(keyID uint, limit int) ([]models.TestLog, error) {
	db := database.GetDB()
	
	var logs []models.TestLog
	query := db.Where("key_id = ?", keyID).Order("created_at DESC")
	
	if limit > 0 {
		query = query.Limit(limit)
	}
	
	if err := query.Find(&logs).Error; err != nil {
		return nil, fmt.Errorf("获取测试日志失败: %w", err)
	}
	
	return logs, nil
}
