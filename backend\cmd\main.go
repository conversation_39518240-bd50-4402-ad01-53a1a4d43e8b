package main

import (
	"log"
	"net/http"

	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"

	"keyhub/internal/api/routes"
	"keyhub/internal/database"
)

func main() {
	// 初始化数据库
	err := database.InitDatabase("keyhub.db")
	if err != nil {
		log.Fatal("Failed to initialize database:", err)
	}

	// 初始化Gin路由
	r := gin.Default()

	// 配置CORS
	config := cors.DefaultConfig()
	config.AllowOrigins = []string{"http://localhost:5173", "http://localhost:3000"}
	config.AllowMethods = []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"}
	config.AllowHeaders = []string{"Origin", "Content-Type", "Accept", "Authorization"}
	r.Use(cors.New(config))

	// 健康检查端点
	r.GET("/health", func(c *gin.Context) {
		c.<PERSON><PERSON>(http.StatusOK, gin.H{
			"status":  "ok",
			"message": "KeyHub API is running",
		})
	})

	// 设置API路由
	routes.SetupRoutes(r)

	log.Println("KeyHub API server starting on :8080")
	if err := r.Run(":8080"); err != nil {
		log.Fatal("Failed to start server:", err)
	}
}
