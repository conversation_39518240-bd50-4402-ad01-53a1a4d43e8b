import { useState, useEffect } from 'react'
import { Plus, Edit, Trash2, TestTube, Loader, DollarSign } from 'lucide-react'
import { getKeys, deleteKey } from '../../api/keys'
import { getProviders } from '../../api/providers'
import { testKey, batchTestKeys, queryKeyBalance, batchQueryBalance } from '../../api/test'
import { type Key } from '../../types/key'
import { type Provider } from '../../types/provider'
import { KeyStatus } from '../../types/common'

const KeyManagement = () => {
  const [keys, setKeys] = useState<Key[]>([])
  const [providers, setProviders] = useState<Provider[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [testingKeys, setTestingKeys] = useState<Set<number>>(new Set())
  const [queryingKeys, setQueryingKeys] = useState<Set<number>>(new Set())
  const [selectedKeys, setSelectedKeys] = useState<Set<number>>(new Set())

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true)
        const [keysData, providersData] = await Promise.all([
          getKeys(),
          getProviders()
        ])
        setKeys(keysData.items || [])
        setProviders(providersData)
      } catch (err) {
        setError(err instanceof Error ? err.message : '获取数据失败')
      } finally {
        setLoading(false)
      }
    }

    fetchData()
  }, [])

  const handleDeleteKey = async (id: number) => {
    if (!confirm('确定要删除这个密钥吗？')) return

    try {
      await deleteKey(id)
      setKeys(keys.filter(key => key.id !== id))
      setSelectedKeys(prev => {
        const newSet = new Set(prev)
        newSet.delete(id)
        return newSet
      })
    } catch (err) {
      alert(err instanceof Error ? err.message : '删除失败')
    }
  }

  const handleTestKey = async (id: number) => {
    if (testingKeys.has(id)) return

    setTestingKeys(prev => new Set(prev).add(id))

    try {
      const testLog = await testKey(id)
      // 更新密钥状态
      setKeys(prevKeys =>
        prevKeys.map(key =>
          key.id === id
            ? {
                ...key,
                status: testLog.status === 1 ? KeyStatus.VALID : KeyStatus.INVALID,
                last_test_time: new Date().toISOString()
              }
            : key
        )
      )
      alert(`测试完成: ${testLog.status === 1 ? '有效' : '无效'}`)
    } catch (err) {
      alert(err instanceof Error ? err.message : '测试失败')
    } finally {
      setTestingKeys(prev => {
        const newSet = new Set(prev)
        newSet.delete(id)
        return newSet
      })
    }
  }

  const handleBatchTest = async () => {
    if (selectedKeys.size === 0) {
      alert('请选择要测试的密钥')
      return
    }

    const keyIds = Array.from(selectedKeys)
    setTestingKeys(new Set(keyIds))

    try {
      const result = await batchTestKeys(keyIds)

      // 更新密钥状态
      setKeys(prevKeys =>
        prevKeys.map(key => {
          const testResult = result.results?.find(r => r.key_id === key.id)
          if (testResult) {
            return {
              ...key,
              status: testResult.status === 1 ? KeyStatus.VALID : KeyStatus.INVALID,
              last_test_time: new Date().toISOString()
            }
          }
          return key
        })
      )

      alert(`批量测试完成: 成功 ${result.success} 个，失败 ${result.failed} 个`)
      setSelectedKeys(new Set())
    } catch (err) {
      alert(err instanceof Error ? err.message : '批量测试失败')
    } finally {
      setTestingKeys(new Set())
    }
  }

  const handleQueryBalance = async (id: number) => {
    if (queryingKeys.has(id)) return

    setQueryingKeys(prev => new Set(prev).add(id))

    try {
      const testLog = await queryKeyBalance(id)

      // 更新密钥余额信息
      if (testLog.status === 1 && testLog.response_data) {
        setKeys(prevKeys =>
          prevKeys.map(key =>
            key.id === id
              ? {
                  ...key,
                  total_balance: testLog.response_data?.total_balance || null,
                  charge_balance: testLog.response_data?.charge_balance || null,
                  gift_balance: testLog.response_data?.gift_balance || null,
                  currency: testLog.response_data?.currency || key.currency,
                  balance_updated_at: new Date().toISOString()
                }
              : key
          )
        )
        alert('余额查询成功')
      } else {
        alert(`余额查询失败: ${testLog.error_message || '未知错误'}`)
      }
    } catch (err) {
      alert(err instanceof Error ? err.message : '余额查询失败')
    } finally {
      setQueryingKeys(prev => {
        const newSet = new Set(prev)
        newSet.delete(id)
        return newSet
      })
    }
  }

  const handleBatchQueryBalance = async () => {
    if (selectedKeys.size === 0) {
      alert('请选择要查询余额的密钥')
      return
    }

    const keyIds = Array.from(selectedKeys)
    setQueryingKeys(new Set(keyIds))

    try {
      const result = await batchQueryBalance(keyIds)

      // 更新密钥余额信息
      setKeys(prevKeys =>
        prevKeys.map(key => {
          const queryResult = result.results?.find(r => r.key_id === key.id)
          if (queryResult && queryResult.status === 1 && queryResult.response_data) {
            return {
              ...key,
              total_balance: queryResult.response_data.total_balance || null,
              charge_balance: queryResult.response_data.charge_balance || null,
              gift_balance: queryResult.response_data.gift_balance || null,
              currency: queryResult.response_data.currency || key.currency,
              balance_updated_at: new Date().toISOString()
            }
          }
          return key
        })
      )

      alert(`批量余额查询完成: 成功 ${result.success} 个，失败 ${result.failed} 个`)
      setSelectedKeys(new Set())
    } catch (err) {
      alert(err instanceof Error ? err.message : '批量余额查询失败')
    } finally {
      setQueryingKeys(new Set())
    }
  }

  const handleSelectKey = (id: number) => {
    setSelectedKeys(prev => {
      const newSet = new Set(prev)
      if (newSet.has(id)) {
        newSet.delete(id)
      } else {
        newSet.add(id)
      }
      return newSet
    })
  }

  const handleSelectAll = () => {
    if (selectedKeys.size === keys.length) {
      setSelectedKeys(new Set())
    } else {
      setSelectedKeys(new Set(keys.map(key => key.id)))
    }
  }

  const getStatusText = (status: KeyStatus) => {
    switch (status) {
      case KeyStatus.VALID:
        return { text: '有效', color: 'text-green-600 bg-green-100' }
      case KeyStatus.INVALID:
        return { text: '无效', color: 'text-red-600 bg-red-100' }
      default:
        return { text: '未测试', color: 'text-gray-600 bg-gray-100' }
    }
  }

  const getProviderName = (providerId: number) => {
    const provider = providers.find(p => p.id === providerId)
    return provider?.name || '未知'
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-gray-500">加载中...</div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-md p-4">
        <div className="text-red-800">错误: {error}</div>
      </div>
    )
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-900">密钥管理</h1>
        <div className="flex space-x-3">
          {selectedKeys.size > 0 && (
            <>
              <button
                onClick={handleBatchTest}
                disabled={testingKeys.size > 0 || queryingKeys.size > 0}
                className="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 disabled:opacity-50 flex items-center space-x-2"
              >
                {testingKeys.size > 0 ? (
                  <Loader size={16} className="animate-spin" />
                ) : (
                  <TestTube size={16} />
                )}
                <span>批量测试 ({selectedKeys.size})</span>
              </button>
              <button
                onClick={handleBatchQueryBalance}
                disabled={testingKeys.size > 0 || queryingKeys.size > 0}
                className="bg-purple-600 text-white px-4 py-2 rounded-md hover:bg-purple-700 disabled:opacity-50 flex items-center space-x-2"
              >
                {queryingKeys.size > 0 ? (
                  <Loader size={16} className="animate-spin" />
                ) : (
                  <DollarSign size={16} />
                )}
                <span>批量查询余额 ({selectedKeys.size})</span>
              </button>
            </>
          )}
          <button className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 flex items-center space-x-2">
            <Plus size={16} />
            <span>添加密钥</span>
          </button>
        </div>
      </div>

      <div className="bg-white rounded-lg shadow overflow-hidden">
        {keys.length === 0 ? (
          <div className="p-6 text-center text-gray-500">
            暂无密钥数据
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    <input
                      type="checkbox"
                      checked={keys.length > 0 && selectedKeys.size === keys.length}
                      onChange={handleSelectAll}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    名称
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    提供商
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    密钥值
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    状态
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    余额
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    最后测试
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    操作
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {keys.map((key) => {
                  const status = getStatusText(key.status)
                  const isSelected = selectedKeys.has(key.id)
                  const isTesting = testingKeys.has(key.id)
                  const isQuerying = queryingKeys.has(key.id)

                  return (
                    <tr key={key.id} className={`hover:bg-gray-50 ${isSelected ? 'bg-blue-50' : ''}`}>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <input
                          type="checkbox"
                          checked={isSelected}
                          onChange={() => handleSelectKey(key.id)}
                          className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                        />
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">
                          {key.name || '未命名'}
                        </div>
                        {key.notes && (
                          <div className="text-sm text-gray-500">{key.notes}</div>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {getProviderName(key.provider_id)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 font-mono">
                        {key.key_value.substring(0, 20)}...
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${status.color}`}>
                          {status.text}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {key.total_balance !== null ? (
                          `${key.total_balance} ${key.currency}`
                        ) : (
                          '-'
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {key.last_test_time ? (
                          new Date(key.last_test_time).toLocaleString('zh-CN')
                        ) : (
                          '未测试'
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="flex space-x-2">
                          <button
                            onClick={() => handleTestKey(key.id)}
                            disabled={isTesting || isQuerying}
                            className="text-blue-600 hover:text-blue-900 disabled:opacity-50"
                            title="测试密钥"
                          >
                            {isTesting ? (
                              <Loader size={16} className="animate-spin" />
                            ) : (
                              <TestTube size={16} />
                            )}
                          </button>
                          <button
                            onClick={() => handleQueryBalance(key.id)}
                            disabled={isTesting || isQuerying}
                            className="text-purple-600 hover:text-purple-900 disabled:opacity-50"
                            title="查询余额"
                          >
                            {isQuerying ? (
                              <Loader size={16} className="animate-spin" />
                            ) : (
                              <DollarSign size={16} />
                            )}
                          </button>
                          <button className="text-indigo-600 hover:text-indigo-900" title="编辑密钥">
                            <Edit size={16} />
                          </button>
                          <button
                            onClick={() => handleDeleteKey(key.id)}
                            className="text-red-600 hover:text-red-900"
                            title="删除密钥"
                          >
                            <Trash2 size={16} />
                          </button>
                        </div>
                      </td>
                    </tr>
                  )
                })}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </div>
  )
}

export default KeyManagement
