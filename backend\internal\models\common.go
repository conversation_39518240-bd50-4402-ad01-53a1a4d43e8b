package models

import (
	"time"
	"gorm.io/gorm"
)

// BaseModel 基础模型，包含通用字段
type BaseModel struct {
	ID        uint           `json:"id" gorm:"primarykey"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`
}

// KeyStatus 密钥状态枚举
type KeyStatus int

const (
	KeyStatusUntested KeyStatus = 0 // 未测试
	KeyStatusValid    KeyStatus = 1 // 有效
	KeyStatusInvalid  KeyStatus = 2 // 无效
)

// TestStatus 测试状态枚举
type TestStatus int

const (
	TestStatusSuccess TestStatus = 1 // 成功
	TestStatusFailed  TestStatus = 2 // 失败
)

// TestType 测试类型枚举
type TestType string

const (
	TestTypeConnectivity TestType = "connectivity" // 连通性测试
	TestTypeBalance      TestType = "balance"      // 余额查询
)
