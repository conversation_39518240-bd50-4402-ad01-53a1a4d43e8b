package database

import (
	"log"

	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
	_ "modernc.org/sqlite"

	"keyhub/internal/models"
)

var DB *gorm.DB

// InitDatabase 初始化数据库连接
func InitDatabase(dsn string) error {
	var err error

	// 配置GORM
	config := &gorm.Config{
		Logger: logger.Default.LogMode(logger.Info),
	}

	// 连接数据库
	DB, err = gorm.Open(sqlite.Dialector{
		DriverName: "sqlite",
		DSN:        dsn,
	}, config)

	if err != nil {
		return err
	}

	// 自动迁移
	err = AutoMigrate()
	if err != nil {
		return err
	}

	// 初始化默认数据
	err = SeedDefaultData()
	if err != nil {
		return err
	}

	log.Println("Database initialized successfully")
	return nil
}

// AutoMigrate 自动迁移数据库表
func AutoMigrate() error {
	return DB.AutoMigrate(
		&models.Provider{},
		&models.Key{},
		&models.TestLog{},
	)
}

// SeedDefaultData 初始化默认数据
func SeedDefaultData() error {
	// 检查是否已有提供商数据
	var count int64
	DB.Model(&models.Provider{}).Count(&count)
	if count > 0 {
		return nil // 已有数据，跳过初始化
	}

	// 创建默认提供商
	providers := []models.Provider{
		{
			Name:            "OpenAI",
			BaseURL:         "https://api.openai.com/v1",
			TestEndpoint:    "/chat/completions",
			BalanceEndpoint: "/dashboard/billing/credit_grants",
			AuthType:        "Bearer",
			SupportsBalance: true,
			Currency:        "USD",
			Config: models.ProviderConfig{
				"model":       "gpt-3.5-turbo",
				"max_tokens":  10,
				"temperature": 0.1,
			},
		},
		{
			Name:            "硅基流动",
			BaseURL:         "https://api.siliconflow.cn/v1",
			TestEndpoint:    "/chat/completions",
			BalanceEndpoint: "/user/info",
			AuthType:        "Bearer",
			SupportsBalance: true,
			Currency:        "CNY",
			Config: models.ProviderConfig{
				"model":       "Qwen/Qwen2.5-7B-Instruct",
				"max_tokens":  10,
				"temperature": 0.1,
			},
		},
		{
			Name:     "自定义",
			BaseURL:  "",
			AuthType: "Bearer",
			Currency: "USD",
			Config:   models.ProviderConfig{},
		},
	}

	for _, provider := range providers {
		if err := DB.Create(&provider).Error; err != nil {
			return err
		}
	}

	log.Println("Default providers created successfully")
	return nil
}

// GetDB 获取数据库实例
func GetDB() *gorm.DB {
	return DB
}
