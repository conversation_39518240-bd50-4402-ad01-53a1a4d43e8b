package handlers

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"keyhub/internal/services/test"
)

// TestHandler 测试处理器
type TestHandler struct {
	testService *test.TestService
}

// NewTestHandler 创建测试处理器
func NewTestHandler() *TestHandler {
	return &TestHandler{
		testService: test.NewTestService(),
	}
}

// TestKey 测试单个密钥
func (h *TestHandler) TestKey(c *gin.Context) {
	id := c.Param("id")
	keyID, err := strconv.ParseUint(id, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "无效的密钥ID",
		})
		return
	}
	
	testLog, err := h.testService.TestKeyConnectivity(uint(keyID))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "测试失败",
			"error":   err.Error(),
		})
		return
	}
	
	c.<PERSON>(http.StatusOK, gin.H{
		"success": true,
		"message": "测试完成",
		"data":    testLog,
	})
}

// BatchTestKeys 批量测试密钥
func (h *TestHandler) BatchTestKeys(c *gin.Context) {
	var req struct {
		KeyIDs []uint `json:"key_ids" binding:"required"`
	}
	
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "无效的请求数据",
			"error":   err.Error(),
		})
		return
	}
	
	if len(req.KeyIDs) == 0 {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "密钥ID列表不能为空",
		})
		return
	}
	
	if len(req.KeyIDs) > 50 {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "一次最多只能测试50个密钥",
		})
		return
	}
	
	testLogs, err := h.testService.BatchTestKeys(req.KeyIDs)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "批量测试失败",
			"error":   err.Error(),
		})
		return
	}
	
	// 统计结果
	var successCount, failedCount int
	for _, log := range testLogs {
		if log.Status == 1 { // TestStatusSuccess
			successCount++
		} else {
			failedCount++
		}
	}
	
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "批量测试完成",
		"data": gin.H{
			"total":   len(testLogs),
			"success": successCount,
			"failed":  failedCount,
			"results": testLogs,
		},
	})
}

// GetTestLogs 获取测试日志
func (h *TestHandler) GetTestLogs(c *gin.Context) {
	id := c.Param("id")
	keyID, err := strconv.ParseUint(id, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "无效的密钥ID",
		})
		return
	}
	
	limitStr := c.DefaultQuery("limit", "10")
	limit, err := strconv.Atoi(limitStr)
	if err != nil {
		limit = 10
	}
	
	logs, err := h.testService.GetTestLogs(uint(keyID), limit)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "获取测试日志失败",
			"error":   err.Error(),
		})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    logs,
	})
}
