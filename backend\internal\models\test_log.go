package models

import (
	"database/sql/driver"
	"encoding/json"
	"errors"
)

// TestLog 测试记录模型
type TestLog struct {
	BaseModel
	KeyID        uint         `json:"key_id" gorm:"not null;index"`
	TestType     TestType     `json:"test_type" gorm:"not null;size:20"`
	Status       TestStatus   `json:"status" gorm:"not null;index"`
	ResponseData ResponseData `json:"response_data" gorm:"type:text"`
	ErrorMessage string       `json:"error_message" gorm:"type:text"`
	Duration     int64        `json:"duration"` // 毫秒
	
	// 关联关系
	Key Key `json:"key,omitempty" gorm:"foreignKey:KeyID"`
}

// ResponseData 响应数据
type ResponseData map[string]interface{}

// Value 实现 driver.Valuer 接口
func (rd ResponseData) Value() (driver.Value, error) {
	if rd == nil {
		return nil, nil
	}
	return json.Marshal(rd)
}

// Scan 实现 sql.Scanner 接口
func (rd *ResponseData) Scan(value interface{}) error {
	if value == nil {
		*rd = nil
		return nil
	}
	
	bytes, ok := value.([]byte)
	if !ok {
		return errors.New("type assertion to []byte failed")
	}
	
	return json.Unmarshal(bytes, rd)
}

// TableName 指定表名
func (TestLog) TableName() string {
	return "test_logs"
}

// IsSuccess 检查测试是否成功
func (tl *TestLog) IsSuccess() bool {
	return tl.Status == TestStatusSuccess
}
