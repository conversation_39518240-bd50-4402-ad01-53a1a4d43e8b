package handlers

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"keyhub/internal/services/balance"
)

// BalanceHandler 余额处理器
type BalanceHandler struct {
	balanceService *balance.BalanceService
}

// NewBalanceHandler 创建余额处理器
func NewBalanceHandler() *BalanceHandler {
	return &BalanceHandler{
		balanceService: balance.NewBalanceService(),
	}
}

// QueryKeyBalance 查询单个密钥余额
func (h *BalanceHandler) QueryKeyBalance(c *gin.Context) {
	id := c.Param("id")
	keyID, err := strconv.ParseUint(id, 10, 32)
	if err != nil {
		c.J<PERSON>(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "无效的密钥ID",
		})
		return
	}
	
	testLog, err := h.balanceService.QueryKeyBalance(uint(keyID))
	if err != nil {
		c.<PERSON>(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "查询余额失败",
			"error":   err.Error(),
		})
		return
	}
	
	c.<PERSON>(http.StatusOK, gin.H{
		"success": true,
		"message": "余额查询完成",
		"data":    testLog,
	})
}

// BatchQueryBalance 批量查询余额
func (h *BalanceHandler) BatchQueryBalance(c *gin.Context) {
	var req struct {
		KeyIDs []uint `json:"key_ids" binding:"required"`
	}
	
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "无效的请求数据",
			"error":   err.Error(),
		})
		return
	}
	
	if len(req.KeyIDs) == 0 {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "密钥ID列表不能为空",
		})
		return
	}
	
	if len(req.KeyIDs) > 20 {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "一次最多只能查询20个密钥的余额",
		})
		return
	}
	
	testLogs, err := h.balanceService.BatchQueryBalance(req.KeyIDs)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "批量查询余额失败",
			"error":   err.Error(),
		})
		return
	}
	
	// 统计结果
	var successCount, failedCount int
	for _, log := range testLogs {
		if log.Status == 1 { // TestStatusSuccess
			successCount++
		} else {
			failedCount++
		}
	}
	
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "批量余额查询完成",
		"data": gin.H{
			"total":   len(testLogs),
			"success": successCount,
			"failed":  failedCount,
			"results": testLogs,
		},
	})
}
