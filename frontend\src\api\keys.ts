import apiClient, { type ApiResponse } from './client'
import { 
  type Key, 
  type CreateKeyRequest, 
  type UpdateKeyRequest, 
  type KeyStats,
  type BatchImportKeysRequest 
} from '../types/key'
import { type PaginatedResponse, type PaginationParams } from '../types/common'

// 获取密钥列表
export const getKeys = async (params?: PaginationParams & {
  provider_id?: number
  status?: number
}): Promise<PaginatedResponse<Key>> => {
  const response: ApiResponse<PaginatedResponse<Key>> = await apiClient.get('/keys', { params })
  return response.data
}

// 获取单个密钥
export const getKey = async (id: number): Promise<Key> => {
  const response: ApiResponse<Key> = await apiClient.get(`/keys/${id}`)
  return response.data
}

// 创建密钥
export const createKey = async (data: CreateKeyRequest): Promise<Key> => {
  const response: ApiResponse<Key> = await apiClient.post('/keys', data)
  return response.data
}

// 更新密钥
export const updateKey = async (data: UpdateKeyRequest): Promise<Key> => {
  const response: ApiResponse<Key> = await apiClient.put(`/keys/${data.id}`, data)
  return response.data
}

// 删除密钥
export const deleteKey = async (id: number): Promise<void> => {
  await apiClient.delete(`/keys/${id}`)
}

// 获取密钥统计信息
export const getKeyStats = async (): Promise<KeyStats> => {
  const response: ApiResponse<KeyStats> = await apiClient.get('/keys/stats')
  return response.data
}

// 批量导入密钥
export const batchImportKeys = async (data: BatchImportKeysRequest): Promise<Key[]> => {
  const response: ApiResponse<Key[]> = await apiClient.post('/keys/batch', data)
  return response.data || []
}
