package handlers

import (
	"encoding/csv"
	"fmt"
	"net/http"
	"strconv"
	"time"

	"keyhub/internal/database"
	"keyhub/internal/models"

	"github.com/gin-gonic/gin"
)

// KeyHandler 密钥处理器
type KeyHandler struct{}

// NewKeyHandler 创建密钥处理器
func NewKeyHandler() *KeyHandler {
	return &KeyHandler{}
}

// GetKeys 获取密钥列表
func (h *KeyHandler) GetKeys(c *gin.Context) {
	db := database.GetDB()

	// 分页参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.<PERSON>("page_size", "20"))
	sortBy := c.DefaultQuery("sort_by", "created_at")
	sortOrder := c.DefaultQuery("sort_order", "desc")

	// 筛选参数
	providerID := c.Query("provider_id")
	status := c.Query("status")

	// 构建查询
	query := db.Model(&models.Key{}).Preload("Provider")

	if providerID != "" {
		query = query.Where("provider_id = ?", providerID)
	}

	if status != "" {
		query = query.Where("status = ?", status)
	}

	// 计算总数
	var total int64
	query.Count(&total)

	// 分页和排序
	offset := (page - 1) * pageSize
	orderClause := sortBy + " " + sortOrder

	var keys []models.Key
	err := query.Order(orderClause).Limit(pageSize).Offset(offset).Find(&keys).Error
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "Failed to fetch keys",
			"error":   err.Error(),
		})
		return
	}

	// 计算总页数
	totalPages := int((total + int64(pageSize) - 1) / int64(pageSize))

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"items":       keys,
			"total":       total,
			"page":        page,
			"page_size":   pageSize,
			"total_pages": totalPages,
		},
	})
}

// GetKey 获取单个密钥
func (h *KeyHandler) GetKey(c *gin.Context) {
	db := database.GetDB()
	id := c.Param("id")

	var key models.Key
	err := db.Preload("Provider").First(&key, id).Error
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"message": "Key not found",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    key,
	})
}

// CreateKey 创建密钥
func (h *KeyHandler) CreateKey(c *gin.Context) {
	db := database.GetDB()

	var req struct {
		ProviderID uint   `json:"provider_id" binding:"required"`
		KeyValue   string `json:"key_value" binding:"required"`
		Name       string `json:"name"`
		Notes      string `json:"notes"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "Invalid request data",
			"error":   err.Error(),
		})
		return
	}

	// 检查提供商是否存在
	var provider models.Provider
	if err := db.First(&provider, req.ProviderID).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "Provider not found",
		})
		return
	}

	// 检查密钥是否已存在
	var existingKey models.Key
	if err := db.Where("key_value = ? AND provider_id = ?", req.KeyValue, req.ProviderID).First(&existingKey).Error; err == nil {
		c.JSON(http.StatusConflict, gin.H{
			"success": false,
			"message": "Key already exists for this provider",
		})
		return
	}

	// 创建密钥
	key := models.Key{
		ProviderID: req.ProviderID,
		KeyValue:   req.KeyValue,
		Name:       req.Name,
		Notes:      req.Notes,
		Status:     models.KeyStatusUntested,
		Currency:   provider.Currency,
	}

	if err := db.Create(&key).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "Failed to create key",
			"error":   err.Error(),
		})
		return
	}

	// 重新加载包含关联数据的密钥
	db.Preload("Provider").First(&key, key.ID)

	c.JSON(http.StatusCreated, gin.H{
		"success": true,
		"message": "Key created successfully",
		"data":    key,
	})
}

// UpdateKey 更新密钥
func (h *KeyHandler) UpdateKey(c *gin.Context) {
	db := database.GetDB()
	id := c.Param("id")

	var key models.Key
	if err := db.First(&key, id).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"message": "Key not found",
		})
		return
	}

	var req struct {
		Name  string `json:"name"`
		Notes string `json:"notes"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "Invalid request data",
			"error":   err.Error(),
		})
		return
	}

	// 更新字段
	key.Name = req.Name
	key.Notes = req.Notes

	if err := db.Save(&key).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "Failed to update key",
			"error":   err.Error(),
		})
		return
	}

	// 重新加载包含关联数据的密钥
	db.Preload("Provider").First(&key, key.ID)

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Key updated successfully",
		"data":    key,
	})
}

// DeleteKey 删除密钥
func (h *KeyHandler) DeleteKey(c *gin.Context) {
	db := database.GetDB()
	id := c.Param("id")

	var key models.Key
	if err := db.First(&key, id).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"message": "Key not found",
		})
		return
	}

	if err := db.Delete(&key).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "Failed to delete key",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Key deleted successfully",
	})
}

// GetKeyStats 获取密钥统计信息
func (h *KeyHandler) GetKeyStats(c *gin.Context) {
	db := database.GetDB()

	var stats struct {
		Total    int64   `json:"total"`
		Valid    int64   `json:"valid"`
		Invalid  int64   `json:"invalid"`
		Untested int64   `json:"untested"`
		Balance  float64 `json:"total_balance"`
	}

	// 总数
	db.Model(&models.Key{}).Count(&stats.Total)

	// 各状态统计
	db.Model(&models.Key{}).Where("status = ?", models.KeyStatusValid).Count(&stats.Valid)
	db.Model(&models.Key{}).Where("status = ?", models.KeyStatusInvalid).Count(&stats.Invalid)
	db.Model(&models.Key{}).Where("status = ?", models.KeyStatusUntested).Count(&stats.Untested)

	// 总余额（仅统计有效密钥）
	var totalBalance float64
	db.Model(&models.Key{}).
		Where("status = ? AND total_balance IS NOT NULL", models.KeyStatusValid).
		Select("COALESCE(SUM(total_balance), 0)").
		Scan(&totalBalance)
	stats.Balance = totalBalance

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    stats,
	})
}

// BatchImportKeys 批量导入密钥
func (h *KeyHandler) BatchImportKeys(c *gin.Context) {
	db := database.GetDB()

	var req struct {
		ProviderID uint `json:"provider_id" binding:"required"`
		Keys       []struct {
			KeyValue string `json:"key_value" binding:"required"`
			Name     string `json:"name"`
			Notes    string `json:"notes"`
		} `json:"keys" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "Invalid request data",
			"error":   err.Error(),
		})
		return
	}

	if len(req.Keys) == 0 {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "Keys list cannot be empty",
		})
		return
	}

	if len(req.Keys) > 100 {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "Cannot import more than 100 keys at once",
		})
		return
	}

	// 检查提供商是否存在
	var provider models.Provider
	if err := db.First(&provider, req.ProviderID).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "Provider not found",
		})
		return
	}

	var importedKeys []models.Key
	var skippedCount int
	var errorCount int

	for _, keyData := range req.Keys {
		// 检查密钥是否已存在
		var existingKey models.Key
		if err := db.Where("key_value = ? AND provider_id = ?", keyData.KeyValue, req.ProviderID).First(&existingKey).Error; err == nil {
			skippedCount++
			continue
		}

		// 创建密钥
		key := models.Key{
			ProviderID: req.ProviderID,
			KeyValue:   keyData.KeyValue,
			Name:       keyData.Name,
			Notes:      keyData.Notes,
			Status:     models.KeyStatusUntested,
			Currency:   provider.Currency,
		}

		if err := db.Create(&key).Error; err != nil {
			errorCount++
			continue
		}

		importedKeys = append(importedKeys, key)
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": fmt.Sprintf("Import completed: %d imported, %d skipped, %d errors",
			len(importedKeys), skippedCount, errorCount),
		"data": gin.H{
			"imported": len(importedKeys),
			"skipped":  skippedCount,
			"errors":   errorCount,
			"keys":     importedKeys,
		},
	})
}

// ExportKeys 导出密钥
func (h *KeyHandler) ExportKeys(c *gin.Context) {
	db := database.GetDB()

	format := c.DefaultQuery("format", "json")
	providerID := c.Query("provider_id")
	status := c.Query("status")

	// 构建查询
	query := db.Model(&models.Key{}).Preload("Provider")

	if providerID != "" {
		query = query.Where("provider_id = ?", providerID)
	}

	if status != "" {
		query = query.Where("status = ?", status)
	}

	var keys []models.Key
	if err := query.Find(&keys).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "Failed to fetch keys",
			"error":   err.Error(),
		})
		return
	}

	switch format {
	case "csv":
		h.exportKeysAsCSV(c, keys)
	case "json":
		h.exportKeysAsJSON(c, keys)
	default:
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "Unsupported format. Use 'json' or 'csv'",
		})
	}
}

// exportKeysAsJSON 导出为JSON格式
func (h *KeyHandler) exportKeysAsJSON(c *gin.Context, keys []models.Key) {
	exportData := make([]map[string]interface{}, len(keys))

	for i, key := range keys {
		exportData[i] = map[string]interface{}{
			"id":                 key.ID,
			"provider_name":      key.Provider.Name,
			"provider_id":        key.ProviderID,
			"key_value":          key.KeyValue,
			"name":               key.Name,
			"status":             key.Status,
			"last_test_time":     key.LastTestTime,
			"total_balance":      key.TotalBalance,
			"charge_balance":     key.ChargeBalance,
			"gift_balance":       key.GiftBalance,
			"currency":           key.Currency,
			"balance_updated_at": key.BalanceUpdatedAt,
			"notes":              key.Notes,
			"created_at":         key.CreatedAt,
			"updated_at":         key.UpdatedAt,
		}
	}

	c.Header("Content-Type", "application/json")
	c.Header("Content-Disposition", fmt.Sprintf("attachment; filename=keyhub_keys_%s.json",
		time.Now().Format("20060102_150405")))

	c.JSON(http.StatusOK, gin.H{
		"success":     true,
		"data":        exportData,
		"count":       len(keys),
		"exported_at": time.Now(),
	})
}

// exportKeysAsCSV 导出为CSV格式
func (h *KeyHandler) exportKeysAsCSV(c *gin.Context, keys []models.Key) {
	c.Header("Content-Type", "text/csv")
	c.Header("Content-Disposition", fmt.Sprintf("attachment; filename=keyhub_keys_%s.csv",
		time.Now().Format("20060102_150405")))

	writer := csv.NewWriter(c.Writer)
	defer writer.Flush()

	// 写入CSV头部
	headers := []string{
		"ID", "Provider Name", "Provider ID", "Key Value", "Name",
		"Status", "Last Test Time", "Total Balance", "Charge Balance",
		"Gift Balance", "Currency", "Balance Updated At", "Notes",
		"Created At", "Updated At",
	}
	writer.Write(headers)

	// 写入数据行
	for _, key := range keys {
		var statusText string
		switch key.Status {
		case models.KeyStatusValid:
			statusText = "Valid"
		case models.KeyStatusInvalid:
			statusText = "Invalid"
		default:
			statusText = "Untested"
		}

		var lastTestTime, balanceUpdatedAt string
		if key.LastTestTime != nil {
			lastTestTime = key.LastTestTime.Format("2006-01-02 15:04:05")
		}
		if key.BalanceUpdatedAt != nil {
			balanceUpdatedAt = key.BalanceUpdatedAt.Format("2006-01-02 15:04:05")
		}

		var totalBalance, chargeBalance, giftBalance string
		if key.TotalBalance != nil {
			totalBalance = fmt.Sprintf("%.2f", *key.TotalBalance)
		}
		if key.ChargeBalance != nil {
			chargeBalance = fmt.Sprintf("%.2f", *key.ChargeBalance)
		}
		if key.GiftBalance != nil {
			giftBalance = fmt.Sprintf("%.2f", *key.GiftBalance)
		}

		row := []string{
			fmt.Sprintf("%d", key.ID),
			key.Provider.Name,
			fmt.Sprintf("%d", key.ProviderID),
			key.KeyValue,
			key.Name,
			statusText,
			lastTestTime,
			totalBalance,
			chargeBalance,
			giftBalance,
			key.Currency,
			balanceUpdatedAt,
			key.Notes,
			key.CreatedAt.Format("2006-01-02 15:04:05"),
			key.UpdatedAt.Format("2006-01-02 15:04:05"),
		}
		writer.Write(row)
	}
}
