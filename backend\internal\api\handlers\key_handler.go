package handlers

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"keyhub/internal/database"
	"keyhub/internal/models"
)

// KeyHandler 密钥处理器
type KeyHandler struct{}

// NewKeyHandler 创建密钥处理器
func NewKeyHandler() *KeyHandler {
	return &KeyHandler{}
}

// GetKeys 获取密钥列表
func (h *KeyHandler) GetKeys(c *gin.Context) {
	db := database.GetDB()
	
	// 分页参数
	page, _ := strconv.Atoi(c.Default<PERSON>uery("page", "1"))
	pageSize, _ := strconv.Atoi(c.<PERSON>("page_size", "20"))
	sortBy := c.<PERSON>("sort_by", "created_at")
	sortOrder := c.<PERSON>("sort_order", "desc")
	
	// 筛选参数
	providerID := c.Query("provider_id")
	status := c.Query("status")
	
	// 构建查询
	query := db.Model(&models.Key{}).Preload("Provider")
	
	if providerID != "" {
		query = query.Where("provider_id = ?", providerID)
	}
	
	if status != "" {
		query = query.Where("status = ?", status)
	}
	
	// 计算总数
	var total int64
	query.Count(&total)
	
	// 分页和排序
	offset := (page - 1) * pageSize
	orderClause := sortBy + " " + sortOrder
	
	var keys []models.Key
	err := query.Order(orderClause).Limit(pageSize).Offset(offset).Find(&keys).Error
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "Failed to fetch keys",
			"error":   err.Error(),
		})
		return
	}
	
	// 计算总页数
	totalPages := int((total + int64(pageSize) - 1) / int64(pageSize))
	
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"items":       keys,
			"total":       total,
			"page":        page,
			"page_size":   pageSize,
			"total_pages": totalPages,
		},
	})
}

// GetKey 获取单个密钥
func (h *KeyHandler) GetKey(c *gin.Context) {
	db := database.GetDB()
	id := c.Param("id")
	
	var key models.Key
	err := db.Preload("Provider").First(&key, id).Error
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"message": "Key not found",
		})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    key,
	})
}

// CreateKey 创建密钥
func (h *KeyHandler) CreateKey(c *gin.Context) {
	db := database.GetDB()
	
	var req struct {
		ProviderID uint   `json:"provider_id" binding:"required"`
		KeyValue   string `json:"key_value" binding:"required"`
		Name       string `json:"name"`
		Notes      string `json:"notes"`
	}
	
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "Invalid request data",
			"error":   err.Error(),
		})
		return
	}
	
	// 检查提供商是否存在
	var provider models.Provider
	if err := db.First(&provider, req.ProviderID).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "Provider not found",
		})
		return
	}
	
	// 检查密钥是否已存在
	var existingKey models.Key
	if err := db.Where("key_value = ? AND provider_id = ?", req.KeyValue, req.ProviderID).First(&existingKey).Error; err == nil {
		c.JSON(http.StatusConflict, gin.H{
			"success": false,
			"message": "Key already exists for this provider",
		})
		return
	}
	
	// 创建密钥
	key := models.Key{
		ProviderID: req.ProviderID,
		KeyValue:   req.KeyValue,
		Name:       req.Name,
		Notes:      req.Notes,
		Status:     models.KeyStatusUntested,
		Currency:   provider.Currency,
	}
	
	if err := db.Create(&key).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "Failed to create key",
			"error":   err.Error(),
		})
		return
	}
	
	// 重新加载包含关联数据的密钥
	db.Preload("Provider").First(&key, key.ID)
	
	c.JSON(http.StatusCreated, gin.H{
		"success": true,
		"message": "Key created successfully",
		"data":    key,
	})
}

// UpdateKey 更新密钥
func (h *KeyHandler) UpdateKey(c *gin.Context) {
	db := database.GetDB()
	id := c.Param("id")
	
	var key models.Key
	if err := db.First(&key, id).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"message": "Key not found",
		})
		return
	}
	
	var req struct {
		Name  string `json:"name"`
		Notes string `json:"notes"`
	}
	
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "Invalid request data",
			"error":   err.Error(),
		})
		return
	}
	
	// 更新字段
	key.Name = req.Name
	key.Notes = req.Notes
	
	if err := db.Save(&key).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "Failed to update key",
			"error":   err.Error(),
		})
		return
	}
	
	// 重新加载包含关联数据的密钥
	db.Preload("Provider").First(&key, key.ID)
	
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Key updated successfully",
		"data":    key,
	})
}

// DeleteKey 删除密钥
func (h *KeyHandler) DeleteKey(c *gin.Context) {
	db := database.GetDB()
	id := c.Param("id")
	
	var key models.Key
	if err := db.First(&key, id).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"message": "Key not found",
		})
		return
	}
	
	if err := db.Delete(&key).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "Failed to delete key",
			"error":   err.Error(),
		})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Key deleted successfully",
	})
}

// GetKeyStats 获取密钥统计信息
func (h *KeyHandler) GetKeyStats(c *gin.Context) {
	db := database.GetDB()
	
	var stats struct {
		Total    int64   `json:"total"`
		Valid    int64   `json:"valid"`
		Invalid  int64   `json:"invalid"`
		Untested int64   `json:"untested"`
		Balance  float64 `json:"total_balance"`
	}
	
	// 总数
	db.Model(&models.Key{}).Count(&stats.Total)
	
	// 各状态统计
	db.Model(&models.Key{}).Where("status = ?", models.KeyStatusValid).Count(&stats.Valid)
	db.Model(&models.Key{}).Where("status = ?", models.KeyStatusInvalid).Count(&stats.Invalid)
	db.Model(&models.Key{}).Where("status = ?", models.KeyStatusUntested).Count(&stats.Untested)
	
	// 总余额（仅统计有效密钥）
	var totalBalance float64
	db.Model(&models.Key{}).
		Where("status = ? AND total_balance IS NOT NULL", models.KeyStatusValid).
		Select("COALESCE(SUM(total_balance), 0)").
		Scan(&totalBalance)
	stats.Balance = totalBalance
	
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    stats,
	})
}
