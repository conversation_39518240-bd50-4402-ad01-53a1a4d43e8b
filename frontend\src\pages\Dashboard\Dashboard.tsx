import { useState, useEffect } from 'react'
import { getKeyStats } from '../../api/keys'
import { type KeyStats } from '../../types/key'

const Dashboard = () => {
  const [stats, setStats] = useState<KeyStats>({
    total: 0,
    valid: 0,
    invalid: 0,
    untested: 0,
    total_balance: 0,
  })
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchStats = async () => {
      try {
        setLoading(true)
        const data = await getKeyStats()
        setStats(data)
      } catch (err) {
        setError(err instanceof Error ? err.message : '获取统计信息失败')
      } finally {
        setLoading(false)
      }
    }

    fetchStats()
  }, [])

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-gray-500">加载中...</div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-md p-4">
        <div className="text-red-800">错误: {error}</div>
      </div>
    )
  }

  return (
    <div>
      <h1 className="text-2xl font-bold text-gray-900 mb-6">控制台</h1>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white p-6 rounded-lg shadow">
          <h3 className="text-sm font-medium text-gray-500">总密钥数</h3>
          <p className="text-2xl font-bold text-gray-900">{stats.total}</p>
        </div>
        <div className="bg-white p-6 rounded-lg shadow">
          <h3 className="text-sm font-medium text-gray-500">有效密钥</h3>
          <p className="text-2xl font-bold text-green-600">{stats.valid}</p>
        </div>
        <div className="bg-white p-6 rounded-lg shadow">
          <h3 className="text-sm font-medium text-gray-500">无效密钥</h3>
          <p className="text-2xl font-bold text-red-600">{stats.invalid}</p>
        </div>
        <div className="bg-white p-6 rounded-lg shadow">
          <h3 className="text-sm font-medium text-gray-500">未测试</h3>
          <p className="text-2xl font-bold text-gray-600">{stats.untested}</p>
        </div>
      </div>

      {stats.total_balance !== undefined && stats.total_balance > 0 && (
        <div className="mt-6">
          <div className="bg-white p-6 rounded-lg shadow">
            <h3 className="text-sm font-medium text-gray-500">总余额</h3>
            <p className="text-2xl font-bold text-blue-600">
              {stats.total_balance.toFixed(2)} {stats.currency || 'USD'}
            </p>
          </div>
        </div>
      )}
    </div>
  )
}

export default Dashboard
