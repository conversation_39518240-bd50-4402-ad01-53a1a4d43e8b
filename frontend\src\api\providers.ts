import apiClient, { type ApiResponse } from './client'
import { type Provider, type CreateProviderRequest, type UpdateProviderRequest } from '../types/provider'

// 获取提供商列表
export const getProviders = async (): Promise<Provider[]> => {
  const response: ApiResponse<Provider[]> = await apiClient.get('/providers')
  return response.data || []
}

// 获取单个提供商
export const getProvider = async (id: number): Promise<Provider> => {
  const response: ApiResponse<Provider> = await apiClient.get(`/providers/${id}`)
  return response.data
}

// 创建提供商
export const createProvider = async (data: CreateProviderRequest): Promise<Provider> => {
  const response: ApiResponse<Provider> = await apiClient.post('/providers', data)
  return response.data
}

// 更新提供商
export const updateProvider = async (data: UpdateProviderRequest): Promise<Provider> => {
  const response: ApiResponse<Provider> = await apiClient.put(`/providers/${data.id}`, data)
  return response.data
}

// 删除提供商
export const deleteProvider = async (id: number): Promise<void> => {
  await apiClient.delete(`/providers/${id}`)
}
