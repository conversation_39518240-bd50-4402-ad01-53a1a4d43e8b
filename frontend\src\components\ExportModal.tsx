import { useState } from 'react'
import { X, Download, FileText, Database } from 'lucide-react'
import { exportKeys } from '../api/keys'
import { type Provider } from '../types/provider'
import { KeyStatus } from '../types/common'

interface ExportModalProps {
  isOpen: boolean
  onClose: () => void
  providers: Provider[]
}

export default function ExportModal({ isOpen, onClose, providers }: ExportModalProps) {
  const [format, setFormat] = useState<'json' | 'csv'>('json')
  const [selectedProviderId, setSelectedProviderId] = useState<number | undefined>(undefined)
  const [selectedStatus, setSelectedStatus] = useState<number | undefined>(undefined)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  if (!isOpen) return null

  const handleExport = async () => {
    setLoading(true)
    setError(null)

    try {
      const filters: { provider_id?: number; status?: number } = {}
      
      if (selectedProviderId) {
        filters.provider_id = selectedProviderId
      }
      
      if (selectedStatus !== undefined) {
        filters.status = selectedStatus
      }

      const blob = await exportKeys(format, filters)
      
      // 创建下载链接
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      
      // 生成文件名
      const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, '')
      const providerName = selectedProviderId 
        ? providers.find(p => p.id === selectedProviderId)?.name || 'unknown'
        : 'all'
      const statusName = selectedStatus !== undefined
        ? ['untested', 'valid', 'invalid'][selectedStatus] || 'unknown'
        : 'all'
      
      link.download = `keyhub_keys_${providerName}_${statusName}_${timestamp}.${format}`
      
      // 触发下载
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)
      
      onClose()
    } catch (err) {
      setError(err instanceof Error ? err.message : '导出失败')
    } finally {
      setLoading(false)
    }
  }

  const getStatusText = (status: number) => {
    switch (status) {
      case KeyStatus.VALID:
        return '有效'
      case KeyStatus.INVALID:
        return '无效'
      default:
        return '未测试'
    }
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-md">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-xl font-semibold">导出密钥</h2>
          <button onClick={onClose} className="text-gray-500 hover:text-gray-700">
            <X size={24} />
          </button>
        </div>

        <div className="space-y-6">
          {/* 导出格式 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              导出格式
            </label>
            <div className="flex space-x-4">
              <label className="flex items-center">
                <input
                  type="radio"
                  value="json"
                  checked={format === 'json'}
                  onChange={(e) => setFormat(e.target.value as 'json' | 'csv')}
                  className="mr-2"
                />
                <FileText size={16} className="mr-1" />
                JSON
              </label>
              <label className="flex items-center">
                <input
                  type="radio"
                  value="csv"
                  checked={format === 'csv'}
                  onChange={(e) => setFormat(e.target.value as 'json' | 'csv')}
                  className="mr-2"
                />
                <Database size={16} className="mr-1" />
                CSV
              </label>
            </div>
          </div>

          {/* 提供商筛选 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              提供商筛选（可选）
            </label>
            <select
              value={selectedProviderId || ''}
              onChange={(e) => setSelectedProviderId(e.target.value ? Number(e.target.value) : undefined)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">全部提供商</option>
              {providers.map(provider => (
                <option key={provider.id} value={provider.id}>
                  {provider.name}
                </option>
              ))}
            </select>
          </div>

          {/* 状态筛选 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              状态筛选（可选）
            </label>
            <select
              value={selectedStatus !== undefined ? selectedStatus : ''}
              onChange={(e) => setSelectedStatus(e.target.value ? Number(e.target.value) : undefined)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">全部状态</option>
              <option value={KeyStatus.UNTESTED}>未测试</option>
              <option value={KeyStatus.VALID}>有效</option>
              <option value={KeyStatus.INVALID}>无效</option>
            </select>
          </div>

          {/* 导出说明 */}
          <div className="bg-blue-50 border border-blue-200 rounded-md p-3">
            <h4 className="text-sm font-medium text-blue-800 mb-1">导出说明</h4>
            <ul className="text-xs text-blue-700 space-y-1">
              <li>• JSON格式：包含完整的密钥信息和元数据</li>
              <li>• CSV格式：适合在Excel等表格软件中查看</li>
              <li>• 导出的文件将自动下载到您的设备</li>
              <li>• 请妥善保管导出的密钥文件</li>
            </ul>
          </div>

          {/* 错误信息 */}
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-md p-3">
              <span className="text-red-700 text-sm">{error}</span>
            </div>
          )}

          {/* 操作按钮 */}
          <div className="flex justify-end space-x-3">
            <button
              onClick={onClose}
              className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
            >
              取消
            </button>
            <button
              onClick={handleExport}
              disabled={loading}
              className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 disabled:opacity-50 flex items-center space-x-2"
            >
              {loading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  <span>导出中...</span>
                </>
              ) : (
                <>
                  <Download size={16} />
                  <span>导出</span>
                </>
              )}
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}
