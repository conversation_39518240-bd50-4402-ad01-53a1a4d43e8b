package models

import (
	"database/sql/driver"
	"encoding/json"
	"errors"
)

// Provider 提供商模型
type Provider struct {
	BaseModel
	Name             string          `json:"name" gorm:"not null;size:100"`
	BaseURL          string          `json:"base_url" gorm:"not null;size:255"`
	TestEndpoint     string          `json:"test_endpoint" gorm:"size:255"`
	BalanceEndpoint  string          `json:"balance_endpoint" gorm:"size:255"`
	AuthType         string          `json:"auth_type" gorm:"default:Bearer;size:50"`
	SupportsBalance  bool            `json:"supports_balance" gorm:"default:false"`
	Currency         string          `json:"currency" gorm:"default:USD;size:10"`
	Config           ProviderConfig  `json:"config" gorm:"type:text"`
	
	// 关联关系
	Keys []Key `json:"keys,omitempty" gorm:"foreignKey:ProviderID"`
}

// ProviderConfig 提供商额外配置
type ProviderConfig map[string]interface{}

// Value 实现 driver.Valuer 接口
func (pc ProviderConfig) Value() (driver.Value, error) {
	if pc == nil {
		return nil, nil
	}
	return json.Marshal(pc)
}

// Scan 实现 sql.Scanner 接口
func (pc *ProviderConfig) Scan(value interface{}) error {
	if value == nil {
		*pc = nil
		return nil
	}
	
	bytes, ok := value.([]byte)
	if !ok {
		return errors.New("type assertion to []byte failed")
	}
	
	return json.Unmarshal(bytes, pc)
}

// TableName 指定表名
func (Provider) TableName() string {
	return "providers"
}
