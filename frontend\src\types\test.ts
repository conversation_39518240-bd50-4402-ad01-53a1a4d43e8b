import { type BaseModel, TestStatus, TestType } from './common'

// 响应数据接口
export interface ResponseData {
  [key: string]: any
}

// 测试记录接口
export interface TestLog extends BaseModel {
  key_id: number
  test_type: TestType
  status: TestStatus
  response_data?: ResponseData
  error_message?: string
  duration: number
}

// 测试请求
export interface TestKeyRequest {
  key_id: number
  test_type?: TestType
}

// 批量测试请求
export interface BatchTestRequest {
  key_ids: number[]
  test_type?: TestType
}

// 测试结果
export interface TestResult {
  key_id: number
  success: boolean
  status: TestStatus
  response_data?: ResponseData
  error_message?: string
  duration: number
}

// 批量测试结果
export interface BatchTestResult {
  total: number
  success: number
  failed: number
  results: TestResult[]
}
