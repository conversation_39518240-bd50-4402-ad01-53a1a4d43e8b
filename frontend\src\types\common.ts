// 基础模型接口
export interface BaseModel {
  id: number
  created_at: string
  updated_at: string
}

// 密钥状态枚举
export enum KeyStatus {
  UNTESTED = 0, // 未测试
  VALID = 1,    // 有效
  INVALID = 2   // 无效
}

// 测试状态枚举
export enum TestStatus {
  SUCCESS = 1, // 成功
  FAILED = 2   // 失败
}

// 测试类型枚举
export enum TestType {
  CONNECTIVITY = 'connectivity', // 连通性测试
  BALANCE = 'balance'            // 余额查询
}

// API响应基础接口
export interface ApiResponse<T = any> {
  success: boolean
  message: string
  data?: T
  error?: string
}

// 分页参数
export interface PaginationParams {
  page: number
  page_size: number
  sort_by?: string
  sort_order?: 'asc' | 'desc'
}

// 分页响应
export interface PaginatedResponse<T> {
  items: T[]
  total: number
  page: number
  page_size: number
  total_pages: number
}
