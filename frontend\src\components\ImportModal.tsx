import { useState } from 'react'
import { X, Upload, FileText, AlertCircle } from 'lucide-react'
import { batchImportKeys } from '../api/keys'
import { getProviders } from '../api/providers'
import { type Provider } from '../types/provider'
import { type Key } from '../types/key'

interface ImportModalProps {
  isOpen: boolean
  onClose: () => void
  onImportSuccess: (keys: Key[]) => void
  providers: Provider[]
}

interface ImportKey {
  key_value: string
  name: string
  notes: string
}

export default function ImportModal({ isOpen, onClose, onImportSuccess, providers }: ImportModalProps) {
  const [selectedProviderId, setSelectedProviderId] = useState<number>(1)
  const [importText, setImportText] = useState('')
  const [importKeys, setImportKeys] = useState<ImportKey[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [importMode, setImportMode] = useState<'text' | 'json'>('text')

  if (!isOpen) return null

  const parseTextInput = () => {
    const lines = importText.trim().split('\n').filter(line => line.trim())
    const keys: ImportKey[] = []
    
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim()
      if (line.startsWith('sk-') || line.includes('sk-')) {
        // 尝试提取密钥值
        const keyMatch = line.match(/sk-[a-zA-Z0-9]+/)
        if (keyMatch) {
          keys.push({
            key_value: keyMatch[0],
            name: `导入密钥 ${i + 1}`,
            notes: '批量导入'
          })
        }
      }
    }
    
    setImportKeys(keys)
    setError(keys.length === 0 ? '未找到有效的密钥格式' : null)
  }

  const parseJsonInput = () => {
    try {
      const data = JSON.parse(importText)
      let keys: ImportKey[] = []
      
      if (Array.isArray(data)) {
        keys = data.map((item, index) => ({
          key_value: item.key_value || item.keyValue || item.key || '',
          name: item.name || `导入密钥 ${index + 1}`,
          notes: item.notes || item.description || '批量导入'
        }))
      } else if (data.data && Array.isArray(data.data)) {
        keys = data.data.map((item: any, index: number) => ({
          key_value: item.key_value || item.keyValue || item.key || '',
          name: item.name || `导入密钥 ${index + 1}`,
          notes: item.notes || item.description || '批量导入'
        }))
      }
      
      setImportKeys(keys)
      setError(keys.length === 0 ? '未找到有效的密钥数据' : null)
    } catch (err) {
      setError('JSON格式错误')
      setImportKeys([])
    }
  }

  const handleParseInput = () => {
    if (importMode === 'json') {
      parseJsonInput()
    } else {
      parseTextInput()
    }
  }

  const handleImport = async () => {
    if (importKeys.length === 0) {
      setError('没有可导入的密钥')
      return
    }

    setLoading(true)
    setError(null)

    try {
      const result = await batchImportKeys(selectedProviderId, importKeys)
      onImportSuccess(result.keys)
      alert(`导入完成：成功 ${result.imported} 个，跳过 ${result.skipped} 个，错误 ${result.errors} 个`)
      onClose()
      setImportText('')
      setImportKeys([])
    } catch (err) {
      setError(err instanceof Error ? err.message : '导入失败')
    } finally {
      setLoading(false)
    }
  }

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    const reader = new FileReader()
    reader.onload = (e) => {
      const content = e.target?.result as string
      setImportText(content)
      
      // 根据文件扩展名自动设置导入模式
      if (file.name.endsWith('.json')) {
        setImportMode('json')
      } else {
        setImportMode('text')
      }
    }
    reader.readAsText(file)
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-4xl max-h-[90vh] overflow-y-auto">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-xl font-semibold">批量导入密钥</h2>
          <button onClick={onClose} className="text-gray-500 hover:text-gray-700">
            <X size={24} />
          </button>
        </div>

        <div className="space-y-6">
          {/* 提供商选择 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              选择提供商
            </label>
            <select
              value={selectedProviderId}
              onChange={(e) => setSelectedProviderId(Number(e.target.value))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              {providers.map(provider => (
                <option key={provider.id} value={provider.id}>
                  {provider.name}
                </option>
              ))}
            </select>
          </div>

          {/* 导入模式选择 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              导入模式
            </label>
            <div className="flex space-x-4">
              <label className="flex items-center">
                <input
                  type="radio"
                  value="text"
                  checked={importMode === 'text'}
                  onChange={(e) => setImportMode(e.target.value as 'text' | 'json')}
                  className="mr-2"
                />
                文本模式（每行一个密钥）
              </label>
              <label className="flex items-center">
                <input
                  type="radio"
                  value="json"
                  checked={importMode === 'json'}
                  onChange={(e) => setImportMode(e.target.value as 'text' | 'json')}
                  className="mr-2"
                />
                JSON模式
              </label>
            </div>
          </div>

          {/* 文件上传 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              上传文件（可选）
            </label>
            <input
              type="file"
              accept=".txt,.json,.csv"
              onChange={handleFileUpload}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          {/* 输入区域 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              密钥数据
            </label>
            <textarea
              value={importText}
              onChange={(e) => setImportText(e.target.value)}
              placeholder={importMode === 'json' 
                ? '粘贴JSON格式的密钥数据...\n例如：[{"key_value": "sk-xxx", "name": "密钥1"}]'
                : '粘贴密钥，每行一个...\nsk-xxx\nsk-yyy'
              }
              className="w-full h-32 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          {/* 解析按钮 */}
          <div className="flex space-x-3">
            <button
              onClick={handleParseInput}
              disabled={!importText.trim()}
              className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 disabled:opacity-50 flex items-center space-x-2"
            >
              <FileText size={16} />
              <span>解析密钥</span>
            </button>
          </div>

          {/* 错误信息 */}
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-md p-3 flex items-center space-x-2">
              <AlertCircle size={16} className="text-red-500" />
              <span className="text-red-700">{error}</span>
            </div>
          )}

          {/* 预览区域 */}
          {importKeys.length > 0 && (
            <div>
              <h3 className="text-lg font-medium mb-3">预览 ({importKeys.length} 个密钥)</h3>
              <div className="max-h-40 overflow-y-auto border border-gray-200 rounded-md">
                <table className="min-w-full">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">密钥值</th>
                      <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">名称</th>
                      <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">备注</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-200">
                    {importKeys.map((key, index) => (
                      <tr key={index}>
                        <td className="px-3 py-2 text-sm text-gray-900 font-mono">
                          {key.key_value.substring(0, 20)}...
                        </td>
                        <td className="px-3 py-2 text-sm text-gray-900">{key.name}</td>
                        <td className="px-3 py-2 text-sm text-gray-500">{key.notes}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          )}

          {/* 操作按钮 */}
          <div className="flex justify-end space-x-3">
            <button
              onClick={onClose}
              className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
            >
              取消
            </button>
            <button
              onClick={handleImport}
              disabled={loading || importKeys.length === 0}
              className="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 disabled:opacity-50 flex items-center space-x-2"
            >
              {loading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  <span>导入中...</span>
                </>
              ) : (
                <>
                  <Upload size={16} />
                  <span>导入 ({importKeys.length})</span>
                </>
              )}
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}
