package models

import (
	"time"
)

// Key 密钥模型
type Key struct {
	BaseModel
	ProviderID       uint       `json:"provider_id" gorm:"not null;index"`
	KeyValue         string     `json:"key_value" gorm:"not null;size:500"`
	Name             string     `json:"name" gorm:"size:100"`
	Status           KeyStatus  `json:"status" gorm:"default:0;index"`
	LastTestTime     *time.Time `json:"last_test_time"`
	TotalBalance     *float64   `json:"total_balance" gorm:"type:decimal(10,2)"`
	ChargeBalance    *float64   `json:"charge_balance" gorm:"type:decimal(10,2)"`
	GiftBalance      *float64   `json:"gift_balance" gorm:"type:decimal(10,2)"`
	Currency         string     `json:"currency" gorm:"default:USD;size:10"`
	BalanceUpdatedAt *time.Time `json:"balance_updated_at"`
	Notes            string     `json:"notes" gorm:"type:text"`
	
	// 关联关系
	Provider Provider  `json:"provider,omitempty" gorm:"foreignKey:ProviderID"`
	TestLogs []TestLog `json:"test_logs,omitempty" gorm:"foreignKey:KeyID"`
}

// TableName 指定表名
func (Key) TableName() string {
	return "keys"
}

// IsValid 检查密钥是否有效
func (k *Key) IsValid() bool {
	return k.Status == KeyStatusValid
}

// HasBalance 检查是否有余额信息
func (k *Key) HasBalance() bool {
	return k.TotalBalance != nil || k.ChargeBalance != nil || k.GiftBalance != nil
}

// GetTotalBalance 获取总余额
func (k *Key) GetTotalBalance() float64 {
	if k.TotalBalance != nil {
		return *k.TotalBalance
	}
	
	total := 0.0
	if k.ChargeBalance != nil {
		total += *k.ChargeBalance
	}
	if k.GiftBalance != nil {
		total += *k.GiftBalance
	}
	return total
}
