package handlers

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"keyhub/internal/database"
	"keyhub/internal/models"
)

// ProviderHandler 提供商处理器
type ProviderHandler struct{}

// NewProviderHandler 创建提供商处理器
func NewProviderHandler() *ProviderHandler {
	return &ProviderHandler{}
}

// GetProviders 获取提供商列表
func (h *ProviderHandler) GetProviders(c *gin.Context) {
	db := database.GetDB()
	
	var providers []models.Provider
	err := db.Find(&providers).Error
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "Failed to fetch providers",
			"error":   err.Error(),
		})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    providers,
	})
}

// GetProvider 获取单个提供商
func (h *ProviderHandler) GetProvider(c *gin.Context) {
	db := database.GetDB()
	id := c.Param("id")
	
	var provider models.Provider
	err := db.First(&provider, id).Error
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"message": "Provider not found",
		})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    provider,
	})
}

// CreateProvider 创建提供商
func (h *ProviderHandler) CreateProvider(c *gin.Context) {
	db := database.GetDB()
	
	var req struct {
		Name            string                    `json:"name" binding:"required"`
		BaseURL         string                    `json:"base_url" binding:"required"`
		TestEndpoint    string                    `json:"test_endpoint"`
		BalanceEndpoint string                    `json:"balance_endpoint"`
		AuthType        string                    `json:"auth_type"`
		SupportsBalance bool                      `json:"supports_balance"`
		Currency        string                    `json:"currency"`
		Config          models.ProviderConfig     `json:"config"`
	}
	
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "Invalid request data",
			"error":   err.Error(),
		})
		return
	}
	
	// 设置默认值
	if req.AuthType == "" {
		req.AuthType = "Bearer"
	}
	if req.Currency == "" {
		req.Currency = "USD"
	}
	if req.Config == nil {
		req.Config = make(models.ProviderConfig)
	}
	
	// 检查名称是否已存在
	var existingProvider models.Provider
	if err := db.Where("name = ?", req.Name).First(&existingProvider).Error; err == nil {
		c.JSON(http.StatusConflict, gin.H{
			"success": false,
			"message": "Provider name already exists",
		})
		return
	}
	
	// 创建提供商
	provider := models.Provider{
		Name:            req.Name,
		BaseURL:         req.BaseURL,
		TestEndpoint:    req.TestEndpoint,
		BalanceEndpoint: req.BalanceEndpoint,
		AuthType:        req.AuthType,
		SupportsBalance: req.SupportsBalance,
		Currency:        req.Currency,
		Config:          req.Config,
	}
	
	if err := db.Create(&provider).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "Failed to create provider",
			"error":   err.Error(),
		})
		return
	}
	
	c.JSON(http.StatusCreated, gin.H{
		"success": true,
		"message": "Provider created successfully",
		"data":    provider,
	})
}

// UpdateProvider 更新提供商
func (h *ProviderHandler) UpdateProvider(c *gin.Context) {
	db := database.GetDB()
	id := c.Param("id")
	
	var provider models.Provider
	if err := db.First(&provider, id).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"message": "Provider not found",
		})
		return
	}
	
	var req struct {
		Name            string                `json:"name"`
		BaseURL         string                `json:"base_url"`
		TestEndpoint    string                `json:"test_endpoint"`
		BalanceEndpoint string                `json:"balance_endpoint"`
		AuthType        string                `json:"auth_type"`
		SupportsBalance bool                  `json:"supports_balance"`
		Currency        string                `json:"currency"`
		Config          models.ProviderConfig `json:"config"`
	}
	
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "Invalid request data",
			"error":   err.Error(),
		})
		return
	}
	
	// 检查名称冲突（排除自己）
	if req.Name != "" && req.Name != provider.Name {
		var existingProvider models.Provider
		if err := db.Where("name = ? AND id != ?", req.Name, provider.ID).First(&existingProvider).Error; err == nil {
			c.JSON(http.StatusConflict, gin.H{
				"success": false,
				"message": "Provider name already exists",
			})
			return
		}
		provider.Name = req.Name
	}
	
	// 更新字段
	if req.BaseURL != "" {
		provider.BaseURL = req.BaseURL
	}
	provider.TestEndpoint = req.TestEndpoint
	provider.BalanceEndpoint = req.BalanceEndpoint
	if req.AuthType != "" {
		provider.AuthType = req.AuthType
	}
	provider.SupportsBalance = req.SupportsBalance
	if req.Currency != "" {
		provider.Currency = req.Currency
	}
	if req.Config != nil {
		provider.Config = req.Config
	}
	
	if err := db.Save(&provider).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "Failed to update provider",
			"error":   err.Error(),
		})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Provider updated successfully",
		"data":    provider,
	})
}

// DeleteProvider 删除提供商
func (h *ProviderHandler) DeleteProvider(c *gin.Context) {
	db := database.GetDB()
	id := c.Param("id")
	
	var provider models.Provider
	if err := db.First(&provider, id).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"message": "Provider not found",
		})
		return
	}
	
	// 检查是否有关联的密钥
	var keyCount int64
	db.Model(&models.Key{}).Where("provider_id = ?", id).Count(&keyCount)
	if keyCount > 0 {
		c.JSON(http.StatusConflict, gin.H{
			"success": false,
			"message": "Cannot delete provider with existing keys",
		})
		return
	}
	
	if err := db.Delete(&provider).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "Failed to delete provider",
			"error":   err.Error(),
		})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Provider deleted successfully",
	})
}
