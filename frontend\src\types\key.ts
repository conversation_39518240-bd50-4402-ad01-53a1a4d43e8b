import { type BaseModel, KeyStatus } from './common'
import { type Provider } from './provider'

// 密钥接口
export interface Key extends BaseModel {
  provider_id: number
  key_value: string
  name?: string
  status: KeyStatus
  last_test_time?: string
  total_balance?: number
  charge_balance?: number
  gift_balance?: number
  currency: string
  balance_updated_at?: string
  notes?: string
  provider?: Provider
}

// 创建密钥请求
export interface CreateKeyRequest {
  provider_id: number
  key_value: string
  name?: string
  notes?: string
}

// 更新密钥请求
export interface UpdateKeyRequest extends Partial<CreateKeyRequest> {
  id: number
}

// 批量导入密钥请求
export interface BatchImportKeysRequest {
  provider_id: number
  keys: Array<{
    key_value: string
    name?: string
    notes?: string
  }>
}

// 密钥统计信息
export interface KeyStats {
  total: number
  valid: number
  invalid: number
  untested: number
  total_balance?: number
  currency?: string
}
