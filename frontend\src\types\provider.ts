import { type BaseModel } from './common'

// 提供商配置接口
export interface ProviderConfig {
  [key: string]: any
}

// 提供商接口
export interface Provider extends BaseModel {
  name: string
  base_url: string
  test_endpoint?: string
  balance_endpoint?: string
  auth_type: string
  supports_balance: boolean
  currency: string
  config: ProviderConfig
}

// 创建提供商请求
export interface CreateProviderRequest {
  name: string
  base_url: string
  test_endpoint?: string
  balance_endpoint?: string
  auth_type?: string
  supports_balance?: boolean
  currency?: string
  config?: ProviderConfig
}

// 更新提供商请求
export interface UpdateProviderRequest extends Partial<CreateProviderRequest> {
  id: number
}
