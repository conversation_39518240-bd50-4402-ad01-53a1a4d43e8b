package routes

import (
	"keyhub/internal/api/handlers"

	"github.com/gin-gonic/gin"
)

// SetupRoutes 设置路由
func SetupRoutes(r *gin.Engine) {
	// 创建处理器实例
	keyHandler := handlers.NewKeyHandler()
	providerHandler := handlers.NewProviderHandler()
	testHandler := handlers.NewTestHandler()
	balanceHandler := handlers.NewBalanceHandler()

	// API路由组
	api := r.Group("/api")
	{
		// 基础信息
		api.GET("/info", func(c *gin.Context) {
			c.JSON(200, gin.H{
				"name":    "KeyHub API",
				"version": "1.0.0",
				"status":  "running",
			})
		})

		// 密钥管理路由
		keys := api.Group("/keys")
		{
			keys.GET("", keyHandler.GetKeys)           // 获取密钥列表
			keys.POST("", keyHandler.CreateKey)        // 创建密钥
			keys.GET("/stats", keyHandler.GetKeyStats) // 获取统计信息
			keys.GET("/:id", keyHandler.GetKey)        // 获取单个密钥
			keys.PUT("/:id", keyHandler.UpdateKey)     // 更新密钥
			keys.DELETE("/:id", keyHandler.DeleteKey)  // 删除密钥
		}

		// 提供商管理路由
		providers := api.Group("/providers")
		{
			providers.GET("", providerHandler.GetProviders)          // 获取提供商列表
			providers.POST("", providerHandler.CreateProvider)       // 创建提供商
			providers.GET("/:id", providerHandler.GetProvider)       // 获取单个提供商
			providers.PUT("/:id", providerHandler.UpdateProvider)    // 更新提供商
			providers.DELETE("/:id", providerHandler.DeleteProvider) // 删除提供商
		}

		// 测试相关路由
		test := api.Group("/test")
		{
			test.POST("/keys/:id", testHandler.TestKey)         // 测试单个密钥
			test.POST("/keys/batch", testHandler.BatchTestKeys) // 批量测试密钥
			test.GET("/keys/:id/logs", testHandler.GetTestLogs) // 获取测试日志
		}
	}
}
