import apiClient, { type ApiResponse } from './client'
import { type TestLog, type TestResult, type BatchTestResult } from '../types/test'

// 测试单个密钥
export const testKey = async (keyId: number): Promise<TestLog> => {
  const response: ApiResponse<TestLog> = await apiClient.post(`/test/keys/${keyId}`)
  return response.data
}

// 批量测试密钥
export const batchTestKeys = async (keyIds: number[]): Promise<BatchTestResult> => {
  const response: ApiResponse<BatchTestResult> = await apiClient.post('/test/keys/batch', {
    key_ids: keyIds
  })
  return response.data
}

// 获取测试日志
export const getTestLogs = async (keyId: number, limit?: number): Promise<TestLog[]> => {
  const params = limit ? { limit } : {}
  const response: ApiResponse<TestLog[]> = await apiClient.get(`/test/keys/${keyId}/logs`, { params })
  return response.data || []
}

// 查询单个密钥余额
export const queryKeyBalance = async (keyId: number): Promise<TestLog> => {
  const response: ApiResponse<TestLog> = await apiClient.post(`/balance/keys/${keyId}`)
  return response.data
}

// 批量查询余额
export const batchQueryBalance = async (keyIds: number[]): Promise<BatchTestResult> => {
  const response: ApiResponse<BatchTestResult> = await apiClient.post('/balance/keys/batch', {
    key_ids: keyIds
  })
  return response.data
}
